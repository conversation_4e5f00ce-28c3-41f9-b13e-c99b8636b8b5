# 42. 测试用例

## 1 引言

### 1.1 编写目的
本文档旨在详细描述针对应用管理子系统的测试用例，确保系统功能满足需求规格说明书中的要求，保证系统质量。

### 1.2 背景
应用管理子系统是SAAS平台的重要组成部分，负责提供应用资源管理、应用管理中心和应用资源目录联动等核心功能。

### 1.3 定义
- 应用系统：指在SAAS平台上注册的应用系统
- 应用功能：指应用系统中的具体功能模块
- 应用资源：指应用系统和应用功能的统称
- 应用生命周期：指应用从注册、审批、发布、变更到下架的全过程

### 1.4 参考资料
- 《04-应用管理子系统.pdf》需求文档
- 《应用管理子系统&应用运营运维功能清单.xlsx》

## 2 测试用例清单

| 用例编号 | 用例名称 | 用例描述 | 优先级 |
| --- | --- | --- | --- |
| TC-AM-001 | 应用系统注册-单个注册 | 验证用户能够通过表单单个注册应用系统 | 高 |
| TC-AM-002 | 应用系统注册-批量注册 | 验证用户能够通过批量方式注册应用系统 | 中 |
| TC-AM-003 | 应用系统管理-列表展示 | 验证系统能够以列表形式展示应用系统资源情况 | 高 |
| TC-AM-004 | 应用系统管理-变更操作 | 验证用户能够对应用系统进行变更操作 | 高 |
| TC-AM-005 | 应用系统管理-审核操作 | 验证用户能够对应用系统进行审核操作 | 高 |
| TC-AM-006 | 应用系统管理-上架操作 | 验证用户能够对应用系统进行上架操作 | 高 |
| TC-AM-007 | 应用系统管理-下架操作 | 验证用户能够对应用系统进行下架操作 | 高 |
| TC-AM-008 | 应用功能注册-单个注册 | 验证用户能够通过表单单个注册应用功能 | 高 |
| TC-AM-009 | 应用功能注册-批量注册 | 验证用户能够通过批量方式注册应用功能 | 中 |
| TC-AM-010 | 应用功能管理-列表展示 | 验证系统能够以列表形式展示应用功能资源情况 | 高 |
| TC-AM-011 | 应用功能管理-变更操作 | 验证用户能够对应用功能进行变更操作 | 高 |
| TC-AM-012 | 应用功能管理-审核操作 | 验证用户能够对应用功能进行审核操作 | 高 |
| TC-AM-013 | 应用功能管理-启用操作 | 验证用户能够对应用功能进行启用操作 | 高 |
| TC-AM-014 | 应用功能管理-停用操作 | 验证用户能够对应用功能进行停用操作 | 高 |
| TC-AM-015 | 应用系统资源状态监控 | 验证系统能够以图形化和数据报表的形式展示应用系统资源状态 | 高 |
| TC-AM-016 | 应用功能资源状态监控 | 验证系统能够以图形化和数据报表的形式展示应用功能资源状态 | 高 |
| TC-AM-017 | 应用生命周期-应用注册 | 验证用户能够在系统中注册新应用 | 高 |
| TC-AM-018 | 应用生命周期-应用审批 | 验证应用在进行注册/启用/停用等操作时需要审批 | 高 |
| TC-AM-019 | 应用生命周期-应用发布 | 验证用户能够填写发布申请信息并在审批通过后正式发布 | 高 |
| TC-AM-020 | 应用生命周期-应用变更 | 验证用户能够对应用信息进行变更且在审批通过后生效 | 高 |
| TC-AM-021 | 应用生命周期-应用下架 | 验证用户能够填写下架申请信息并在审批通过后下架 | 高 |
| TC-AM-022 | 应用管理中心-应用申报 | 验证系统为应用建设单位提供应用建设申报流程 | 高 |
| TC-AM-023 | 应用管理中心-应用注册 | 验证系统支持应用注册申请并满足应用基本信息要求 | 高 |
| TC-AM-024 | 应用管理中心-应用测试 | 验证系统支持对接应用服务测试环境管理 | 中 |
| TC-AM-025 | 应用管理中心-应用发布 | 验证系统支持应用发布前的合规性检查 | 高 |
| TC-AM-026 | 应用管理中心-应用下线 | 验证系统支持应用下线功能 | 高 |
| TC-AM-027 | 应用管理中心-流程审批 | 验证系统支持对接任务审批中心进行流程审批 | 高 |
| TC-AM-028 | 应用资源目录同步 | 验证系统能够进行应用资源目录的去重、合并处理 | 高 |
| TC-AM-029 | 应用资源目录上报 | 验证系统能够完成本级资源目录向上级的上报 | 高 |
| TC-AM-030 | 应用资源目录下发 | 验证系统能够完成本级资源目录向下级的下发 | 高 |

## 3 测试用例详细设计

### 3.1 应用系统管理测试用例

#### TC-AM-001：应用系统注册-单个注册

**用例标识**：TC-AM-001

**测试项**：应用系统注册-单个注册

**用例描述**：验证用户能够通过表单单个注册应用系统

**预置条件**：
1. 用户已登录系统并拥有应用系统注册权限
2. 系统已准备好接收应用系统注册请求

**测试步骤**：
1. 进入应用系统注册页面
2. 填写应用系统基本信息，包括：应用系统编号、名称、事权单位、上线日期、下线日期、应用系统分类、业务使用场景等
3. 点击"提交"按钮

**预期结果**：
1. 系统接收注册信息并进行有效性校验
2. 校验通过后，系统创建应用系统记录并显示成功提示
3. 新注册的应用系统显示在应用系统列表中

**实际结果**：

**是否通过**：

**备注**：

#### TC-AM-002：应用系统注册-批量注册

**用例标识**：TC-AM-002

**测试项**：应用系统注册-批量注册

**用例描述**：验证用户能够通过批量方式注册应用系统

**预置条件**：
1. 用户已登录系统并拥有应用系统批量注册权限
2. 系统已准备好接收批量注册请求
3. 用户已准备好符合要求格式的批量注册数据文件

**测试步骤**：
1. 进入应用系统批量注册页面
2. 选择或上传符合格式要求的批量注册数据文件
3. 点击"批量注册"按钮

**预期结果**：
1. 系统接收批量注册数据并进行格式校验
2. 校验通过后，系统批量创建应用系统记录并显示批量注册结果
3. 批量注册的应用系统显示在应用系统列表中

**实际结果**：

**是否通过**：

**备注**：

#### TC-AM-003：应用系统管理-列表展示

**用例标识**：TC-AM-003

**测试项**：应用系统管理-列表展示

**用例描述**：验证系统能够以列表形式展示应用系统资源情况

**预置条件**：
1. 用户已登录系统并拥有应用系统管理权限
2. 系统中已存在多个应用系统记录

**测试步骤**：
1. 进入应用系统管理页面
2. 查看应用系统列表展示

**预期结果**：
1. 系统以列表形式展示所有应用系统资源情况
2. 列表包含应用系统的关键信息：编号、名称、状态、版本号等
3. 列表支持翻页、排序和筛选功能

**实际结果**：

**是否通过**：

**备注**：

#### TC-AM-004：应用系统管理-变更操作

**用例标识**：TC-AM-004

**测试项**：应用系统管理-变更操作

**用例描述**：验证用户能够对应用系统进行变更操作

**预置条件**：
1. 用户已登录系统并拥有应用系统变更权限
2. 系统中已存在可变更的应用系统

**测试步骤**：
1. 进入应用系统管理页面
2. 选择目标应用系统，点击"变更"按钮
3. 修改应用系统相关信息
4. 点击"提交"按钮

**预期结果**：
1. 系统接收变更信息并进行有效性校验
2. 校验通过后，系统更新应用系统记录并显示成功提示
3. 应用系统列表中显示更新后的应用系统信息

**实际结果**：

**是否通过**：

**备注**：

### 3.2 应用功能管理测试用例

#### TC-AM-008：应用功能注册-单个注册

**用例标识**：TC-AM-008

**测试项**：应用功能注册-单个注册

**用例描述**：验证用户能够通过表单单个注册应用功能

**预置条件**：
1. 用户已登录系统并拥有应用功能注册权限
2. 系统中已存在至少一个应用系统
3. 系统已准备好接收应用功能注册请求

**测试步骤**：
1. 进入应用功能注册页面
2. 选择所属应用系统
3. 填写应用功能基本信息，包括：功能编号、名称、功能使用场景、上线日期、下线日期、在用状态等
4. 点击"提交"按钮

**预期结果**：
1. 系统接收注册信息并进行有效性校验
2. 校验通过后，系统创建应用功能记录并显示成功提示
3. 新注册的应用功能显示在应用功能列表中

**实际结果**：

**是否通过**：

**备注**：

### 3.3 应用资源监控测试用例

#### TC-AM-015：应用系统资源状态监控

**用例标识**：TC-AM-015

**测试项**：应用系统资源状态监控

**用例描述**：验证系统能够以图形化和数据报表的形式展示应用系统资源状态

**预置条件**：
1. 用户已登录系统并拥有应用资源监控权限
2. 系统中已存在多个应用系统并有访问量数据

**测试步骤**：
1. 进入应用系统资源状态监控页面
2. 查看各类应用系统资源监控图表和数据

**预期结果**：
1. 系统以图形化和数据报表的形式展示所有应用系统资源的访问总量
2. 显示访问变化趋势和访问用户分布
3. 能够展示单个应用系统资源运行状态、访问量、访问变化趋势、访问用户分布
4. 支持按时间段筛选数据展示

**实际结果**：

**是否通过**：

**备注**：

### 3.4 应用生命周期管理测试用例

#### TC-AM-017：应用生命周期-应用注册

**用例标识**：TC-AM-017

**测试项**：应用生命周期-应用注册

**用例描述**：验证用户能够在系统中注册新应用

**预置条件**：
1. 用户已登录系统并拥有应用注册权限

**测试步骤**：
1. 进入应用注册页面
2. 填写应用基本信息
3. 点击"提交"按钮

**预期结果**：
1. 系统接收注册信息并进行有效性校验
2. 校验通过后，创建新应用记录并显示成功提示
3. 新应用进入审批流程

**实际结果**：

**是否通过**：

**备注**：

### 3.5 应用管理中心功能测试用例

#### TC-AM-022：应用管理中心-应用申报

**用例标识**：TC-AM-022

**测试项**：应用管理中心-应用申报

**用例描述**：验证系统为应用建设单位提供应用建设申报流程

**预置条件**：
1. 用户已登录系统并拥有应用申报权限

**测试步骤**：
1. 进入应用申报页面
2. 填写应用建设申报信息，包括业务应用的基本信息
3. 点击"提交"按钮

**预期结果**：
1. 系统接收申报信息并进行有效性校验
2. 校验通过后，创建应用申报记录并显示成功提示
3. 申报记录进入审批流程

**实际结果**：

**是否通过**：

**备注**：

### 3.6 应用资源目录联动测试用例

#### TC-AM-028：应用资源目录同步

**用例标识**：TC-AM-028

**测试项**：应用资源目录同步

**用例描述**：验证系统能够进行应用资源目录的去重、合并处理

**预置条件**：
1. 用户已登录系统并拥有资源目录管理权限
2. 系统已接收到上级或下级的应用资源目录数据

**测试步骤**：
1. 进入资源目录同步页面
2. 触发资源目录同步操作
3. 查看同步结果

**预期结果**：
1. 系统根据应用系统资源编号进行去重、合并处理
2. 形成完整的应用资源目录
3. 同步完成后显示成功提示

**实际结果**：

**是否通过**：

**备注**：

## 4 附录

### 4.1 修订历史

| 版本号 | 修订日期 | 修订人 | 修订内容 |
| --- | --- | --- | --- |
| V1.0 | 2024-07-24 | AI助手 | 初始版本 |

### 4.2 缩略语

- SAAS: Software as a Service (软件即服务)
- API: Application Programming Interface (应用程序接口) 