应用资源管理功能
#应用系统管理
我司完全满足招标文件“#应用系统管理”所有指标要求，指标项包括满足对大数据平台应用系统进行编目、发布以及状态控制等功能的需求，实现应用资源的灵活配置和有效管理。提供应用系统编目、应用系统注册、应用系统审核、应用系统更新、应用系统停用以及应用系统启用等管理能力等。从功能设计、流程逻辑、接口设计等进行详细阐述，部分功能提供原型设计截图，详细设计内容如下文所示：
满足对大数据平台应用系统进行编目、发布以及状态控制等功能的需求，实现应用资源的灵活配置和有效管理。
提供应用系统编目、应用系统注册、应用系统审核、应用系统更新、应用系统停用以及应用系统启用等管理能力。
应用系统管理提供以下功能：
1、应用系统注册内容符合《数据治理技术要求 应用资源目录》中应用系统数据项的规定。
2、唯一标识符：按照《数据治理技术要求 应用资源目录》应用系统数据项中应用系统编号的规定，自动生成唯一的标识符；
3、标准符合性检查：支持对应用资源数据完整性和标准一致性检查，数据完整性检查的主要目的是保证所有必选的元数据已经赋值，标准一致性检查的主要目标是保证已填写的元数据取值符合《数据治理技术要求 应用资源目录》应用系统数据项的规定。
4、支持根据指定规则基于唯一标识符生成应用系统二维码。
5、支持应用系统基本信息展示包括应用系统编号、应用系统名称、应用系统事权单位代码、应用系统管理单位代码、应用系统承建单位名称、应用系统运维单位名称等，形成应用系统的知识产权。
6、支持对外提供应用系统唯一标识符查询接口；提供应用系统二维码查询接口；提供应用系统基本信息查询接口；提供应用系统注册、审核、更新、启用和停用服务接口。
7、支持以资源目录的形式展示各类应用系统，可根据应用系统分类进行范围筛选，同时可通过应用唯一标识、应用资源名称进行模糊检索，提供应用系统详情钻取展示功能，可以查看应用系统的详细信息。
功能设计
应用系统管理应用系统编目、应用系统注册、应用系统审核、应用系统更新、应用系统停用以及应用系统启用等管理能力，实现对应用系统资源的灵活配置和有效管理。具体功能如下：
应用系统编目
依照应用系统目录编号的要求对应用系统进行编目，并统一存储，实现应用系统目录的统一管理，形成完整的应用系统目录。
应用系统注册
应用系统注册支持用户通过页面表单将应用资源注册到目录中，并根据指定规则生成应用系统二维码。应用系统注册界面如下图所示：

图  应用系统注册界面图
应用系统审核
应用资源审核支持用户在应用资源目录上注册/启用/停用应用系统，经审批中心管理者审核通过后向外发布。应用系统审核界面如下图所示：

图  审批中心界面图

图  审批详情界面图
在应用管理平台工作台，点击我的申请，可查看所有申请中和已办结的申请信息。

图  工作台界面图

图  待审核申请列表界面图

图  已办结申请列表界面图
应用系统更新
当应用资源发生变化时，应用系统更新支持用户通过页面表单及时对已注册的应用系统进行更新，并将更新后的应用系统进行审核、发布。应用系统更新界面如下图所示：

图  应用系统更新界面图

图  应用系统更新表单
应用系统停用以及应用系统启用
支持系统管理员或用户控制应用资源的可用状态。当应用资源暂时失效/恢复使用时，及时停用/启用相关的应用系统；并将更新后的应用系统信息发送到审批中心等待管理员进行审核、发布。应用系统启用界面如下图所示：

图  应用系统启用界面图

图  应用系统启用申请
应用系统停用界面如下图所示：

图  应用系统停用界面图

图  应用系统停用申请
应用系统列表
展示已注册的资源目录信息，点击应用名称可查看当前应用系统基本信息以及应用系统二维码。应用系统列表界面如下图所示：

图  应用系统列表界面图
点击应用名称，可查看当前应用详情。如下图所示：

图  应用详情界面图
流程逻辑
应用系统资源注册流程逻辑

图  应用系统资源注册流程图
1）应用系统有两种注册方式，单个应用系统注册和批量应用系统导入。
2）若是单个应用系统注册，通过在注册页面填写表单并提交，即可完成应用注册。
3） 若是批量应用系统导入，需下载模板文件，填写待注册的应用系统信息并上传文件，完成批量应用系统注册。
4）注册成功后的应用会在应用资源目录列表中展示，应用状态默认为“已注册”。
应用系统审核流程逻辑

图  应用系统审核流程图
用户提交应用系统注册/启用/停用等申请，管理员审核后，用户可在资源目录查看审核结果。
应用系统更新流程逻辑

图  应用系统更新流程图
用户提交应用系统更新申请，管理员审核后，用户可在资源目录查看更新后的信息。
应用系统启用/停用流程逻辑

图  应用系统启用/停用流程图
用户提交应用系统启用/停用申请，管理员审核后，用户可在资源目录查看结果。