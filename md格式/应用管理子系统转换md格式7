应用管理中心功能
△应用管理中心
我司完全满足招标文件“△应用管理中心”所有指标要求，指标项包括应用申报、应用注册、应用测试、应用发布、应用下线、流程审批。从功能设计、流程逻辑、接口设计等进行详细阐述，部分功能提供原型设计截图，详细设计内容如下文所示：
1、应用申报
为应用建设单位提供应用建设申报流程，支持录入业务应用的基本信息，包括应用名称、应用场景、性能指标、功能模块、建设单位、建设周期、建设方案等。
支持提取应用建设方案，提取并形成方案摘要。
支持新建、保存、提交、审批、状态查看等功能。
2、应用注册
支持应用注册申请，满足应用建设单位注册应用基本信息的需求。
支持关联应用申报审批流程，自动获取应用基本信息，并基于申报信息，对应用信息进行补充，补充内容包括基本信息、应用详情等。
基本信息：包括应用名称、建设单位（警种信息）、建设单位联系人、承建单位（应用厂商）、承建单位联系人。
应用详情：包括应用描述、应用logo、应用访问地址(域名)、应用权限申请审批责任人信息等。
支持域名及IP申请：支持应用建设单位申请业务系统的公安网侧域名、数据域侧域名以及域名映射的IP地址、端口、应用发布URL。
支持根据应用名称按照应用域名命名的相关规范自动推荐公安网侧域名、数据域侧域名，支持域名的重名校验。
支持对接应用管理平台、将应用自动注册至应用资源目录中。
3、应用测试
支持对接应用服务测试环境管理，为应用建设方提供完整的应用服务测试环境，支持开通测试专用账号、模拟构造数据服务、应用服务等。
4、应用发布
（1）应用发布检查
支持应用发布前的合规性检查，包括数据共享情况、警种业务数据分级分类情况、是否与零信任体系对接、是否获取入网资格、是否与运营运维对接、是否完成安全检测。
支持将上述各部分按需分解为检查点，支持应用发布申请方针对每个检查点提供证明，如零信任对接测试报告、自动关联应用域名及IP申请结果、等保测评报告、第三方测评报告、系统密码应用安全性评估报告等。
（2）应用部署发布及更新
支持将警种业务系统域名注册到公安大数据平台门户申请，支持应用正式版本发布申请。
支持应用版本更新申请，支持记录历史版本信息，支持更新信息推送，在应用更新、升级时，支持调用统一消息中心向所有关联用户发送更新维护公告。
5、应用下线
应用下线是整个应用完成历史使命，退出服务前的最后一个环节。
支持应用服务下线申请，支持调用统一消息中心向所有关联用户发送应用下线公告及后续承接应用信息。
支持强制下线功能，当安全保障、运营运维等系统检测应用存在重大安全隐患或其他安全风险时，可强制下线应用。
支持配置应用下线申请审批流程。
6、流程审批
支持对接任务审批中心、实现应用开发工程中的任务审批、流程管理。
功能设计
应用申报
支持为应用建设单位提供应用建设申报流程，用户录入业务应用的基本信息，信息包括应用名称、应用场景、性能指标、功能模块、建设单位、建设周期、建设方案等。
支持提取应用建设方案，并形成方案摘要。
支持应用新建、保存、提交、审批、状态查看等功能。
界面如下图所示：

图  应用申报界面图

图  应用申报审批界面图
应用注册
支持应用注册申请，满足应用建设单位注册应用基本信息的需求。
支持关联应用申报审批流程，自动获取应用基本信息，并基于申报的信息，对应用信息进行补充，补充内容包括基本信息、应用详情等。
基本信息：包括应用名称、建设单位（警种信息）、建设单位联系人、承建单位（应用厂商）、承建单位联系人等。
应用详情：包括应用描述、应用logo、应用访问地址(域名)、应用权限申请审批责任人信息等。
支持域名及IP申请：支持应用建设单位申请业务系统的公安网侧域名、数据域侧域名以及域名映射的IP地址、端口、应用发布URL等。
支持根据应用名称按照应用域名命名的相关规范自动推荐公安网侧域名、数据域侧域名，支持域名的重名校验。
支持对接应用管理平台，将应用自动注册至应用资源目录中。

图  应用注册界面图
应用测试
支持对接应用服务测试环境管理，为应用建设方提供完整的应用服务测试环境，支持开通测试专用账号、模拟构造数据服务、应用服务等。
应用测试功能确保应用在发布前达到预定的质量标准，确保应用的稳定性和可靠性。
应用发布
应用发布功能主要包括应用发布检查和应用部署发布及更新。
（1）应用发布检查
支持应用发布前的合规性检查，检查项包括数据共享情况、警种业务数据分级分类情况、是否与零信任体系对接、是否获取入网资格、是否与运营运维对接、是否完成安全检测。
支持将上述各部分按需分解为检查点，支持应用发布申请方针对每个检查点提供证明，如零信任对接测试报告、自动关联应用域名及IP申请结果、等保测评报告、第三方测评报告、系统密码应用安全性评估报告等。检查通过后方可发布。

图  应用发布检查界面图
（2）应用部署发布及更新
支持将警种业务系统域名注册到公安大数据平台门户申请，支持应用正式版本发布申请。
支持应用版本更新申请，支持记录历史版本信息，支持更新信息推送，在应用更新、升级时，支持调用统一消息中心向所有关联用户发送更新维护公告。
应用下线
应用下线是整个应用完成历史使命，退出服务前的最后一个环节。
支持应用服务下线申请，支持调用统一消息中心向所有关联用户发送应用下线公告及后续承接应用信息。
支持强制下线功能，当安全保障、运营运维等系统检测应用存在重大安全隐患或其他安全风险时，可强制下线应用。
支持配置应用下线申请审批流程。用户发起应用下线申请，审批通过后应用下线。

图  应用下线申请界面图
流程审批
支持对接任务审批中心，实现应用开发工程中的任务审批、流程管理。
在整个应用生命周期中，通过流程审批功能，确保所有关键操作都经过适当的审查和批准。
流程逻辑
应用申报流程逻辑
应用申报流程如下：

图  应用申报流程图
1）应用申报有两种方式，单个应用申报和批量应用申报。
2）若是单个应用申报，通过在申报页面填写表单并提交，即可完成应用申报。
3） 若是批量应用导入，需下载模板文件，填写待申报的应用信息并上传文件，完成批量应用申报。
4）申报成功后的应用会在应用资源目录列表中展示。
应用注册流程逻辑
应用注册流程如下：

图  应用注册流程图
1）关联应用申报审批流程，自动获取应用基本信息。
2）对应用信息进行补充，补充内容包括基本信息、应用详情等。
3）对接应用管理平台、将应用自动注册至应用资源目录中。
应用测试流程逻辑
应用测试流程如下：

图  应用测试流程图
1）应用建设方发起应用测试。
2）对接应用服务测试环境管理，可获取完整的应用服务测试环境，开通测试专用账号、模拟构造数据服务、应用服务等。
3）将测试资源反馈给应用建设方。
应用发布流程逻辑
应用发布流程如下：

图  应用发布流程图
应用发布前先进行合规性检查，合规后提交应用发布申请，管理员审核通过后，用户可在应用门户查看发布的应用。
应用更新流程如下：

图  应用更新流程图
1）提交应用更新申请。
2）管理员审核。
3）通过后，支持记录历史版本信息。
4）向所有关联用户发送更新维护公告。
5）开始更新。
6）更新完成，结束。
应用下线流程逻辑
应用下线流程如下：

图  应用下线流程图
用户提交应用下线申请，管理员审核通过后，向所有关联用户发送应用下线公告及后续承接应用信息，应用从应用门户下线。
流程审批流程逻辑
流程审批流程如下：

图  流程审批逻辑图
用户提交应用申报/注册/发布/下线等申请，管理员审核后，用户可在资源目录查看审核结果。
接口设计
应用申报接口设计
应用申报的服务接口，详细接口设计如下表所示。
表 应用申报接口设计表
接口描述	应用申报接口
URL	appmarket/app/resources/DeclareAppResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	申报对象信息集， 
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
            {
                "YYXTBH":"A-************-0001", 
                "YYXTMC":"XXX部XXX局XXX系统",
                "YYXTCJ":"应用场景",
                "YYXTXNZB":"性能指标",
				"YYXTGNMK":"功能模块",
				"YYXTJSDW":"建设单位",
				"YYXTJSZQ":"建设周期",
                "YYXTJSFA":"建设方案"
            }, 
            {
                "YYXTBH":"A-************-0002",
                "YYXTMC":"XXX部XXX局XXX系统",
                "YYXTCJ":"应用场景",
                "YYXTXNZB":"性能指标",
				"YYXTGNMK":"功能模块",
				"YYXTJSDW":"建设单位",
				"YYXTJSZQ":"建设周期",
                "YYXTJSFA":"建设方案"
            }
        ]
    }
}
请求参数	appmarket/app/resources/DeclareAppResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
应用注册接口设计
应用信息注册到应用资源目录的服务接口，详细接口设计如下表所示。
表 应用注册接口设计表
接口描述	应用注册接口
URL	appmarket/app/resources/RegisterAppResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	注册对象信息集，GA/DSJ 234-2019中的对象
ResourceType	string		是	注册的信息对象类型，GA/DSJ 234-2019中的对象（0：应用系统信息，1：应用功能信息）
DataInfo	对象数组		是	注册信息集，当注册的信息对象类型为应用系统信息时，应符合GA/DSJ 234-2019中的应用系统数据项（应用系统编号需要必填）；
当注册的信息对象为应用功能信息时，应符合GA/DSJ 234-2019中的应用功能数据项
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
            {
                //《GA/DSJ 234-2019 公安大数据处理 数据治理 应用资源目录技术要求》中的对象（0：应用系统信息:1：应用功能信息）
                "ResourceType" : "0",
                //注册的应用系统数据项
                "DataInfo" : [
                    {
                        "YYXTBH":"A-************-0001", 
                        "YYXTMC":"XXX部XXX局XXX系统",
                        "YYXTMS":"系统描述",
                        "YYXTBZ":"系统版本",
                        "YYXTDZ":"系统地址"
                    }, 
                    {
                        "YYXTBH":"A-************-0002",
                        "YYXTMC":"XXX部XXX局XXX系统",
                        "YYXTMS":"系统描述",
                        "YYXTBZ":"系统版本",
                        "YYXTDZ":"系统地址"
                    }
                 ] 
            }
        ]
    }
}
请求参数	appmarket/app/resources/RegisterAppResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
应用测试接口设计
测试专用账号开通的服务接口，详细接口设计如下表所示。
表 测试专用账号开通接口设计表
接口描述	测试专用账号开通接口
URL	appmarket/resource/test/enableAcc
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
YYXTBH	string		是	应用编号
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "YYXTBH":"A-************-0001"     
    }
}
请求参数	appmarket/resource/test/enableAcc
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常",
	"Account" : "TXBDYYYFW",
	"Password" : "000000"
}
应用发布接口设计
应用发布的服务接口，详细接口设计如下表所示。
表 应用发布接口设计表
接口描述	应用发布接口
URL	appmarket/app/resources/ReleaseAppResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	发布对象信息集 
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
            {
                "YYXTBH":"A-************-0001", 
                "YYXTMC":"XXX部XXX局XXX系统"
            }, 
            {
                "YYXTBH":"A-************-0002",
                "YYXTMC":"XXX部XXX局XXX系统"
            }
        ]
    }
}
请求参数	appmarket/app/resources/ReleaseAppResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
应用下线接口设计
应用下线的服务接口，详细接口设计如下表所示。
表 应用下线接口设计表
接口描述	应用下线接口
URL	appmarket/app/resources/RemoveAppResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	要下架的应用信息
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
			{
                "YYXTBH":"A-************-0001", 
                "APPLYID":"2932"
            }, 
            {
                "YYXTBH":"A-************-0002",
                "APPLYID":"2234"
            }
        ]
    }
}
请求参数	appmarket/app/resources/RemoveAppResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
流程审批接口设计
流程审批的服务接口，详细接口设计如下表所示。
表 流程审批接口设计表
接口描述	流程审批接口
URL	appmarket/app/resources/ProcessApproveResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	审核的应用信息
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
			{
                "YYXTBH":"A-************-0001", 
                "APPLYID":"1932",
				"RESULT":"1"
            }, 
            {
                "YYXTBH":" A-************-0002",
                "APPLYID":"2074",
				"RESULT":"1"
            }
        ]
    }
}
请求参数	appmarket/app/resources/ProcessApproveResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
#应用资源目录联动
我司完全满足招标文件“#应用资源目录联动”所有指标要求，指标项包括应用资源目录同步、应用资源目录分发。从功能设计、流程逻辑、接口设计等进行详细阐述，部分功能提供原型设计截图，详细设计内容如下文所示：
应用资源目录主要功能包括应用资源目录同步和应用资源目录分发，实现部、省、市之间应用资源目录的上报与下发。
1、应用资源目录同步
支持通过同步指令实现与下级目录联动，要求下级同步来源目录进行服务资源目录上报。
支持由部本级将下级部门所汇聚的应用资源目录进行统一编排，形成完整的应用资源信息存储。
2、应用资源目录分发
支持根据各级应用资源目录要求向下级进行应用资源目录分发，下级应用资源目录管理接收上级所分发的应用资源目录，实现本地应用资源目录的更新。
功能设计
应用资源目录联动主要功能包括应用资源目录同步和应用资源目录分发，实现部、省、市之间应用资源目录的上报与下发。
应用资源目录同步
支持发送资源同步指令，下级进行应用资源目录上报，支持将接收到的下级应用资源目录，与本地资源目录去重合并，统一编排后形成完整的应用资源目录。
应用管理平台点击“资源同步”按钮，将接收到的资源目录与本地资源目录去重、合并后，以列表的形式展示完整的应用资源目录。
图  本地应用资源目录同步界面图

图  同步后应用资源目录列表界面图
应用资源目录分发
支持将本级应用资源目录向下级进行分发，支持以列表的形式展示本级应用资源目录。下级接收上级所分发的应用资源目录，并与本地资源目录进行资源同步，实现本地应用资源目录的更新。
应用管理平台点击“本地应用资源目录管理”，点击 “下发”按钮，对单个应用资源进行分发；选择需下发的应用资源目录，点击“下发”按钮，并选择发往的下级，进行批量分发。

图  单个应用资源目录分发界面图

图  批量应用资源目录分发界面图

图  选择下发地界面图
流程逻辑
应用资源目录同步流程逻辑
应用资源目录同步流程如下：

图  应用资源目录同步流程图
发送资源同步指令，下级进行应用资源目录上报。部本级将接收到的下级应用资源目录，与本地资源目录去重合并，统一编排后形成完整的应用资源目录。
应用资源目录分发流程逻辑
应用资源目录分发流程如下：

图  应用资源目录分发流程图
用户可以对单个应用资源进行分发，或是选择多个应用资源目录，进行批量下发。若下发成功，则结束流程；若下发失败，需重新选择应用资源目录进行下发。
接口设计
应用资源目录同步接口设计
应用资源目录上报的服务接口，详细接口设计如下表所示。
表 应用资源目录上报接口设计表
接口描述	应用资源目录上报接口
URL	appmarket/resource/linkage/ReportResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	应用资源目录信息
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
            {
                "YYXTBH":"B-************-0001", 
                "YYXTMC":"XXX省XXX系统",
                "YYXTMS":"系统描述",
                "YYXTBZ":"系统版本",
                "YYXTDZ":"系统地址"
            }, 
            {
                "YYXTBH":"B-************-0002", 
                "YYXTMC":"XXX省XXX系统"
                "YYXTMS":"系统描述",
                "YYXTBZ":"系统版本",
                "YYXTDZ":"系统地址"
            }
        ]
    }
}
请求参数	appmarket/resource/linkage/ReportResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
应用资源目录分发接口设计
应用资源目录分发的服务接口，详细接口设计如下表所示。
表 应用资源目录分发接口设计表
接口描述	应用资源目录分发接口
URL	appmarket/resource/linkage/DistributeResource
请求方式	Post
请求类型	
返回类型	*/*
参数名	数据类型	参数类型	是否必填	说明
Resources	对象数组		是	应用资源目录信息
状态码	描述	说明
0200	正常	
0300	权限异常	
0400	服务异常	
9900	其他异常	
返回属性名	类型	说明
MessageStatus	string	消息状态码
MessageSequence	string	消息流水号
Remark	string	备注，可用于解释消息状态码或其他自定义内容
示例
{
    "From":"************",
    "To":"************",
    "MessageSequence":"2019010714141200001",
    "RequestParam":{
        "Resources":[
            {
                "YYXTBH":"A-************-0001", 
                "YYXTMC":" XXX部XXX局XXX系统",
                "YYXTMS":"系统描述",
                "YYXTBZ":"系统版本",
                "YYXTDZ":"系统地址"
            }, 
            {
                "YYXTBH":"A-************-0002", 
                "YYXTMC":" XXX部XXX局XXX系统"
                "YYXTMS":"系统描述",
                "YYXTBZ":"系统版本",
                "YYXTDZ":"系统地址"
            }
        ]
    }
}
请求参数	appmarket/resource/linkage/DistributeResource
返回值	{
	"MessageStatus" : "0200",
	"MessageSequence" : "2019010714141200001",
	"Remark" : "正常"
}
