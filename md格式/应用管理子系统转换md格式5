#应用资源监控
我司完全满足招标文件“#应用资源监控”所有指标要求，指标项包括1、支持对应用资源运行状态信息、访问量信息等数据进行实时采集与统计功能。2、支持图形化展示与数据报表统计的能力。从功能设计、流程逻辑、接口设计等进行详细阐述，部分功能提供原型设计截图，详细设计内容如下文所示：
功能设计
应用资源监控支持对应用资源的运行状态信息、访问量信息等数据进行实时采集与统计功能，支持图形化展示与数据报表统计。支持以折线图和饼状图的形式展示应用系统资源和应用功能访问变化趋势和访问用户分布，支持以目录的形式展示各应用系统资源和功能资源运行状态详情。
1）应用系统资源状态监控：展示所有应用系统资源访问情况和运行状态，点击应用名称可查看当前应用访问详情。
应用管理平台点击“应用系统资源状态监控”，可查看所有应用系统资源访问概览，以列表的形式展示应用系统资源详细信息，点击“访问详情”可查看对应应用的访问详情。

图  应用系统资源状态监控概览界面图

图  应用系统资源状态监控列表界面图

图  单个应用系统资源运行状态监控界面图
2）应用功能资源状态监控：展示所有应用功能资源访问情况和运行状态，点击应用功能名称可查看应用功能访问详情。
应用管理平台点击“应用功能资源状态监控”，可查看所有应用功能资源访问概览，以列表的形式展示应用功能资源详细信息，点击“访问详情”可查看对应应用功能的访问详情。
图  应用功能资源状态监控概览

图  应用功能资源状态监控列表

图  单个应用功能资源运行状态监控
流程逻辑
应用资源监控流程如下：

图  应用资源监控流程图
首先从数据库中获取到应用系统资源和应用功能资源的访问数据。访问量和用户分布数据由前端处理后，以折线图和饼状图的形式展示资源的访问变化趋势和访问用户分布；再由访问数据，判断资源运行状态为‘正常’、‘繁忙’或是‘异常’，并在应用资源监控页面展示。
