△应用功能管理
我司完全满足招标文件“△应用功能管理”所有指标要求，指标项包括满足对大数据平台应用功能进行编目、发布以及状态控制等功能的需求，实现对应用功能合规性和时效性的管理。提供应用功能编目、应用功能注册、应用功能审核、应用功能更新、应用功能停用以及应用功能启用等管理能力等。从功能设计、流程逻辑、接口设计等进行详细阐述，部分功能提供原型设计截图，详细设计内容如下文所示：
满足对大数据平台应用功能进行编目、发布以及状态控制等功能的需求，实现对应用功能合规性和时效性的管理。
提供应用功能编目、应用功能注册、应用功能审核、应用功能更新、应用功能停用以及应用功能启用等管理能力。
应用功能管理提供以下功能：
1、应用功能注册内容符合《数据治理技术要求 应用资源目录》中应用系统功能数据项的规定。
2、唯一标识符：按照《数据治理技术要求 应用资源目录》应用功能数据项中功能编号的规定，自动生成唯一的标识符。
3、标准符合性检查：支持对应用功能元数据完整性和标准一致性检查，元数据完整性检查的主要目的是保证所有必选的元数据已经赋值，标准一致性检查的主要目标是保证已填写的元数据取值符合《数据治理技术要求 应用资源目录》应用功能数据项的规定。
4、支持根据指定规则基于唯一标识符生成应用功能二维码。
5、支持应用功能基本信息展示包括所属应用系统编号、功能编号、功能名称、功能说明、上级功能编号等，形成应用系统的知识产权。
6、支持对外提供应用功能唯一 标识符查询接口；提供应用功能二维码查询接口；提供应用功能基本信息查询接口；提供应用功能注册、审核、更新、启用和停用服务接口。
7、支持以资源目录的形式展示各类应用功能，可根据应用功能分类进行范围筛选，同时可通过功能编号、功能名称进行模糊检索，提供应该功能详情钻取展示功能，可以查看应用功能的详细信息。
功能设计
应用功能管理支持应用功能编目、应用功能注册、应用功能审核、应用功能更新、应用功能停用以及应用功能启用等管理能力，实现对应用功能合规性和时效性的管理。
应用功能编目
依照有关应用功能目录编号的要求对应用功能进行编目，并统一存储，完成应用功能目录的统一管理，形成完整的应用功能目录。
应用功能注册
应用功能注册支持用户通过页面表单将应用功能注册到已发布的应用资源中，并根据指定规则生成应用功能二维码。应用功能注册界面如下图所示：

图  应用功能注册界面图
应用功能审核
应用功能审核支持用户在应用资源目录上注册/启用/停用应用系统所属功能，经审批中心管理者审核通过后向外发布。应用功能审核界面如下图所示：

图  审批中心界面图

图  审核详情界面图
应用功能更新
当应用资源目录中某个应用资源的应用功能发生变化时，应用功能更新支持用户通过页面表单及时对该应用资源下的应用功能进行更新，并将更新的应用功能进行审核、发布。应用功能更新界面如下图所示：

图  应用功能更新界面图

图  应用功能更新界面图

图  应用功能更新界面图
应用功能停用
应用功能停用支持系统管理员或用户控制应用资源目录中应用功能的可用状态。当应用功能暂时失效时，及时停用相关的应用功能，停用的应用功能信息进行审核、发布。应用功能停用界面如下图所示：

图  应用功能停用界面图

图  应用功能停用申请
应用功能启用
应用功能启用支持系统管理员或用户控制应用资源目录中应用功能的可用状态。当应用功能恢复使用时，及时重新启用相关的应用功能，并将启用的应用功能信息进行审核、发布。应用功能启用界面如下图所示：

图  应用功能启用界面图

图  应用功能启用申请


