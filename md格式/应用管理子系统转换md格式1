应用管理子系统
系统设计
需求分析
总体需求
提供应用资源管理功能，包括应用资源目录数据项管理、应用资源目录管理、应用资源目录门户、应用资源目录监控以及应用资源目录联动；
提供应用资源管理功能服务化能力，以接口API的方式对外暴露服务接口；
提供应用资源目录管理功能服务化能力，包括应用资源目录注册、审核、更新、停用和启用开放API；
提供应用资源目录联动功能服务化能力，包括应用资源目录的汇聚和分发开放API。
应用资源管理功能
应用资源管理包括应用系统管理、应用功能管理、应用资源监控和应用生命周期管理四大方面。
应用系统管理
应用系统管理提供了应用系统注册、应用系统变更、应用系统上架、下架等功能，实现对应用资源的灵活配置和有效管理。
1）应用系统注册
可以以单个应用注册或者批量导入的方式完成应用系统注册，具体包括应用系统的编号、名称、事权单位、上线日期、下线日期、应用系统分类、业务使用场景等内容。
2）应用系统管理
以列表的形式展示应用系统资源情况，对于应用系统支持变更、审核、上架、下架生命周期管理功能。
应用功能管理
应用功能管理提供应用功能注册、应用功能变更、应用功能停用、应用功能启用等功能，实现对应用功能合规性和时效性的管理。
1）应用功能注册
可以以单个功能注册或者批量注册的方式完成应用功能注册，具体包括应用功能所属应用系统编号、功能编号、功能名称、功能使用场景、上线日期、下线日期、在用状态等内容。
2）应用功能管理
以列表的形式展示应用功能资源情况，对于应用功能支持变更、审核、启用、停用的生命周期管理功能。
应用资源监控
应用资源监控以图形化展示和数据报表统计的形式，对应用资源运行状态信息、访问量信息等数据进行实时采集与统计，使管理员能够实时直观的掌握应用资源运行信息。
1）应用系统资源状态监控
以图形化和数据报表的形式，展示所有应用系统资源的访问总量、访问变化趋势和访问用户分布，展示单个应用系统资源运行状态、访问量、访问变化趋势、访问用户分布。
2）应用功能资源状态监控
以图形化和数据报表的形式，展示所有应用所包含的功能资源的访问总量、访问变化趋势和访问用户分布，展示单个应用包含的功能资源运行状态、访问量、访问变化趋势、访问用户分布。
应用生命周期管理
应用生命周期管理提供了应用全生命周期管理，涵盖应用的注册、审批、发布、变更和下架等关键环节，监控和管理应用的整个生命周期。
应用注册：支持用户在系统中注册新应用，支持填写应用基本信息。
应用审批：用户在进行注册/启用/停用等操作时，需审批后生效。
应用发布：填写发布申请信息，审批通过后正式发布。
应用变更：对应用信息进行变更，审批通过后生效。
应用下架：填写下架申请信息，审批通过后下架。
应用管理中心功能
应用管理中心
提供了应用申报、应用注册、应用测试、应用发布、应用下线、流程审批等功能。
应用申报：为应用建设单位提供应用建设申报流程，支持录入业务应用的基本信息。
应用注册：支持应用注册申请，满足应用建设单位注册应用基本信息的需求。
应用测试：支持对接应用服务测试环境管理，为应用建设方提供完整的应用服务测试环境。
应用发布：支持应用发布前的合规性检查，支持应用部署发布及更新。
应用下线：支持应用下线，调用统一消息中心向所有关联用户发送应用下线公告及后续承接应用信息。
流程审批：支持对接任务审批中心、实现应用开发工程中的任务审批、流程管理。
应用资源目录联动
应用资源目录联动主要功能包括应用资源目录同步和应用资源目录分发，实现省、市之间应用资源目录的上报和下发。
应用资源目录同步
本级应用资源目录在接收到上级或是下级应用资源目录后，按照应用系统资源编号进行去重、合并，实现应用资源目录的同一编排，形成完整的应用资源目录。
应用资源目录分发
应用资源目录分发包括应用资源目录上报和应用资源目录下发。
1）应用资源目录上报
通过接收上级资源目录同步指令，完成本级资源目录向上级的上报。
2）应用资源目录下发
通过接收下级资源目录同步指令，完成本级资源目录向下级的下发。
架构设计
图 应用管理子系统功能架构
应用管理子系统主要包括应用资源管理功能和应用管理中心功能。
应用资源管理功能包括应用系统管理、应用功能管理、应用资源监控和应用生命周期管理四大方面。应用系统管理提供应用系统编目、应用系统注册、应用系统审核、应用系统更新、应用系统停用以及应用系统启用等管理能力。应用功能管理提供应用功能编目、应用功能注册、应用功能审核、应用功能更新、应用功能停用以及应用功能启用等管理能力。应用资源监控支持对应用资源运行状态信息、访问量信息等数据进行实时采集与统计功能。应用生命周期管理包括应用注册、应用审批、应用发布、应用变更、应用下架等功能。
应用管理中心功能包括应用管理中心和应用资源目录联动。应用管理中心提供应用申报、应用注册、应用测试、应用发布、应用下线、流程审批。应用资源目录联动主要功能包括应用资源目录同步和应用资源目录分发，实现部、省、市之间应用资源目录的上报与下发。
业务流程
应用资源注册
用户进行应用资源注册流程如下：

图 应用资源注册流程图
1）应用资源有两种注册方式，单个应用资源注册和批量应用资源导入。
2）单个应用资源注册，通过在注册页面填写表单并提交，即可完成应用资源注册。
3） 批量应用资源导入需下载模板文件，填写待注册的应用资源信息并上传文件，完成批量应用资源注册。
4）注册成功后的应用资源会在应用资源目录列表中展示。
应用资源更新
应用资源更新流程如下：

图 应用资源更新流程图
1）当用户需要对应用资源进行更新时，首先判断是否为应用版本更新。
2）如果是版本更新，用户需要填写版本变更申请，等待审核人员审核。若审核通过，变更后的版本信息会在应用资源目录列表中展示；若不通过，则用户需要重新填写版本变更申请，并等待再次审核。
3）如果是应用资源基本信息变更，则直接在变更页面填写表单并提交，变更后的应用资源信息会在应用资源目录列表中展示。
应用资源启用、停用
应用资源启用流程如下：

图 应用资源启用、停用流程图
1）当用户想要启用应用资源时，首先需要填写应用资源启用申请，等待审核人员审核，应用状态变为启用中。
2）若审核通过，应用资源状态更新，流程结束。
3）若审核不通过，应用资源状态更新，流程结束；若用户仍想启用应用资源，则需重新填写启用申请，并等待审核。
应用资源停用流程与启用相同。
数据库设计
应用系统表
表 应用系统表
中文表名	应用系统表
英文表名	yygl_app
功    能	　
列英文名	列中文名	数据类型	空/非空	说明
id	主键	serial8	not null	　
app_id	应用系统编号	varchar(100)	not null	生成规则:A-事权单位代码-4位流水号
app_name	应用系统名称	varchar(200)	not null	　
abstract_desc	应用系统说明	text	not null	　
descripton	应用功能描述	text	　	　
icon_url	应用图标	text	not null	默认图标
app_url	应用系统访问地址	varchar(500)	not null	　
app_deploy_url	应用系统部署地址	varchar(500)	not null	访问应用系统的物理部署的完整URL或完整的协议
police_dict	应用系统所属警种	varchar(500)	　	　
visit_count	访问次数	int8	　	　
app_status	应用状态	int4	not null	1:注册、2上架中 3已上架，4:下架中，5:已下架
uptime	上线日期	int8	not null	　
offtime	下线日期	int8	　	　
is_featured	是否特色应用	int4	not null	0:否 1：是  默认0
is_completed	应用建设状态	int4	not null	0：建设中 1：建设完成， 
creator_id	创建用户id	varchar(50)	not null	　
create_time	创建时间	int8	not null	　
update_id	更新用户id	varchar(50)	　	　
update_time	更新时间	int8	　	　
need_apply	是否需要申请	int4	not null	0：不需要 1：需要
weight	权值	int4	not null	默认为0 可根据它来调整首页应用展示顺序
build_depart	应用系统建设单位名称	varchar(200)	　	　
build_liaison	应用系统建设单位联系人	varchar(50)	　	　
build_depart_tel	应用系统建设单位电话	varchar(20)	　	　
respond_depart	应用系统承建单位名称	varchar(200)	　	　
respond_liaison	应用系统承建单位联系人	varchar(50)	　	　
respond_depart_tel	应用系统承建单位电话	varchar(20)	　	　
onner_depart	应用系统事权单位代码	varchar(50)	not null	　
manage_depart	应用系统管理单位代码	varchar(50)	not null	　
operation_depart	应用系统运维单位名称	varchar(200)	　	　
operation_depart_liaison 	应用系统运维单联系人	varchar(50)	　	　
operation_depart_tel	应用系统运维单联系电话	varchar(20)	　	　
app_build_type	应用建设类型	int4	　	1：ace 2：sailor 3：thirdpart
app_src_type	应用来源系统种类	varchar(50)	not null	描述数据的来源应用系统分类，各业务警种根据自身实际业务自定义
app_type_id	应用系统分类代码	varchar(20)	not null	　
app_get_type	应用获取方式	varchar(20)	not null	01：侦控 02：管控 03：管理 04：公开
	（对部标，可能没用）			
app_auth_level	应用系统认证级别	varchar(200)	not null	　
app_use_flag	系统在用标识	varchar(20)	not null	0：未用 1：在用 
				
app_constraction	系统架构类型	varchar(20)	not null	1：B/S系统架构  2：C/S系统架构
				
app_use_scene	业务使用场景代码	varchar(200)	not null	可多值，用半角逗号分隔
app_apply_sum	总申请次数	int4	　	每次申请时，同步更新
app_grade_avg	平均评分	varchar(20)	　	每次评分后，同步更新
app_version	版本号	varchar(20)	　	每次版本变更申请通过后，同步更新
app_project_url	保存项目服务的IP端口项目名	varchar(500)	not null	如：http://IP:端口/项目名
inner_app_id	应用内部ID 	varchar(100)	　	应用内部ID,业务自定义,建议与apollo一致
city_code	应用地市编码	varchar(10)	　	　
应用功能表
表 应用功能表
中文表名	应用功能表
英文表名	yygl_app_function
功    能	　
列英文名	列中文名	数据类型	空/非空	说明
id	主键	serial8	not null	　
app_id	应用系统编号	varchar(50)	not null	　
func_id	功能编号	varchar(50)	not null	　
func_pid	上级功能编号	varchar(50)	not null	默认值0 顶级菜单
func_name	功能名称	varchar(50)	not null	　
func_desc	功能说明	text	not null	　
func_use_scene	功能使用场景代码	varchar(50)	not null	　
func_sec_level	涉及安全隐私类别	varchar(50)	　	　
func_uri	功能URI	varchar(200)	　	　
use_status	在用状态	varchar(20)	not null	0-未用 1-在用 
uptime	上线日期	int8	　	　
offtime	下线日期	int8	　	　
inner_func_id	应用功能Id	varchar(100)	　	业务注册bdp的optId
应用申请记录表
表 应用申请记录表
中文表名	应用申请记录表
英文表名	yygl_app_apply
功    能	　
列英文名	列中文名	数据类型	空/非空	说明
id	编号	serial8	not null	　
user_id	当前用户编号	varchar(100)	not null	　
apply_status	申请单状态	varchar(10)	not null	1 已申请 2 已通过 3 已驳回 4 已办结
apply_point	申请单当前节点	varchar(50)	not null	　
apply_app_id	被申请应用ID	varchar(50)	not null	　
apply_content	申请事由	text	not null	　
apply_time	申请时间	int8	not null	　
start_time	申请有效时间，开始时间	int8	not null	　
end_time	申请有效时间，结束时间	int8	　	　
status	是否生效	int4	not null	0-否 1-是
file_name	附件名称	varchar(50)	　	　
flow_instance_id	关联审批中心流程ID	varchar(50)	not null	　
apply_type	申请类型	varchar(10)	　	　
用户应用状态表
表 用户应用状态表
中文表名	用户应用状态表
英文表名	yygl_app_status
功    能	　
列英文名	列中文名	数据类型	空/非空	说明
id	主键	serial8	not null	　
app_id	应用ID	varchar(50)	not null	　
user_id	当前用户编号	varchar(100)	not null	　
status	当前状态	varchar(10)	not null	1 申请中；2续订中；3使用中；4已退订 5即将到期 6未申请
status_time	状态结束时间	int8	　	　
update_time	更新时间	int8	not null	　
应用视频图片记录表
表 应用视频图片记录表
中文表名	应用视频图片记录表
英文表名	yygl_app_file
功    能	　
列英文名	列中文名	数据类型	空/非空	说明
id	主键	serial8	not null	　
app_id	所属应用系统号	varchar(50)	not null	　
file_icon	文件URL	text	not null	　
file_name	上传文件名	varchar(100)	not null	用于前端展示
file_ename	上传文件别名	varchar(100)	not null	用于记录保存到服务器的文件别名
file_type	文件类型	int4	not null	0：图片 1:视频 2:图标
create_time	创建日期	int8	not null	　
应用版本号记录表
表 应用版本号记录表
中文表名	应用版本号记录表
英文表名	yygl_app_version
功    能	用于记录应用版本号
列英文名	列中文名	数据类型	空/非空	说明
id	id，PK	serial8	not null	　
app_id	应用id	varchar(50)	not null	　
version	应用版本号	varchar(20)	not null	　
release_note	版本描述	text	　	　
status	是否生效	varchar(10)	not null	0-否 1-是
user_id	用户id	varchar(100)	not null	　
create_time	创建时间	int8	not null	　
update_time	更新时间	int8	　	　
initial_value	是否初始版本（1：初始版本 时间：后续版本）	Int8	　	是否初始版本（1：初始版本 时间：后续版本）
业务字典表
表 业务字典表
中文表名	业务字典表
英文表名	yygl_app_dict
功    能	用于存放数据类字典信息
列英文名	列中文名	数据类型	空/非空	说明
id	主键,PK	serial8	Not Null	　
dict_code	字典编号	varchar(128)	Not Null	　
dict_name	字典值名称	varchar(128)	Not Null	　
dict_type	字典值种类	varchar(128)	Not Null	　
dict_value_desc	字典描述	varchar(128)	Not Null	　
dict_p_code	父字典类型，无父字典类型则为0	varchar(128)	Not Null	　
fh_dic_id	对应FH字典值id	varchar(128)	　	　
status	是否使用	varchar(128)	Not Null	0: 停用,1: 在用 默认为1在用
版本记录表
表 版本记录表
中文表名	版本记录表
英文表名	yygl_base_version
功    能	用于存放版本记录
列英文名	列中文名	数据类型	空/非空	说明
id	主键，PK	serial8	　	　
sysname	模块名	varchar(255)	　	　
version	版本号	varchar(255)	　	　
ipaddress	Ip地址	varchar(255)	　	　
utime	安装时间	int8	　	　
type	安装类型	int8	　	　
prename	父模块名	varchar(255)	　	　
opflag	操作标志位	int8	　	　
status	状态	int8	　	　
remark	备注	varchar(255)	　	　
