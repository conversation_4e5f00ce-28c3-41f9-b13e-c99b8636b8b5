import os
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import pandas as pd
import docx
from docx.shared import Pt, Cm, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
import io
import numpy as np
import re
import time
import sys

# 设置图片目录路径
image_dir = '/Users/<USER>/Documents/limy工作目录/项目相关/验收与评测/BU总线/licf提供-250724下达任务/png格式/批量输出为图片'

# 输出文档路径
output_doc = os.path.join(os.path.dirname(image_dir), '42.测试用例-ai生成拆分后.docx')

# 首先读取测试用例模板
template_path = os.path.join(image_dir, '测试用例模板.png')

# 禁用PIL的DecompressionBombWarning
Image.MAX_IMAGE_PIXELS = None

def preprocess_image(img):
    """预处理图片以提高OCR识别率"""
    # 调整对比度
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.5)
    
    # 锐化
    img = img.filter(ImageFilter.SHARPEN)
    
    # 转换为灰度图
    img = img.convert('L')
    
    # 二值化处理
    threshold = 200
    img = img.point(lambda p: p > threshold and 255)
    
    return img

def extract_text_from_image(image_path, max_size=4000):
    """从图片中提取文本，处理大图片"""
    try:
        print(f"处理图片: {os.path.basename(image_path)}")
        img = Image.open(image_path)
        
        # 如果图片太大，先缩小
        width, height = img.size
        scale_factor = 1
        
        if width > max_size or height > max_size:
            scale_factor = max_size / max(width, height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = img.resize((new_width, new_height), Image.LANCZOS)
            print(f"  调整图片大小: {width}x{height} -> {new_width}x{new_height}")
        
        # 预处理图片
        img = preprocess_image(img)
        
        # 对于非常大的图片，分块处理
        if width * height > 10000000:  # 1000万像素以上的图片分块处理
            print(f"  图片过大，进行分块处理")
            block_height = 2000  # 每块高度
            texts = []
            
            # 计算块数
            num_blocks = (img.height + block_height - 1) // block_height
            
            for i in range(num_blocks):
                y_start = i * block_height
                y_end = min((i + 1) * block_height, img.height)
                
                print(f"  处理块 {i+1}/{num_blocks}: 行 {y_start}-{y_end}")
                
                # 裁剪图片块
                block = img.crop((0, y_start, img.width, y_end))
                
                # OCR识别
                block_text = pytesseract.image_to_string(block, lang='chi_sim')
                texts.append(block_text)
                
                # 释放内存
                del block
            
            text = "\n".join(texts)
        else:
            # 对于较小的图片，直接处理
            text = pytesseract.image_to_string(img, lang='chi_sim')
        
        return text
    except Exception as e:
        print(f"处理图片 {image_path} 时出错: {e}")
        return ""

def parse_template(template_text):
    """解析测试用例模板，提取表头和结构"""
    print("解析测试用例模板...")
    
    # 常见的测试用例表头
    default_headers = ['用例编号', '测试项', '测试说明', '前置条件', '测试步骤', '预期结果', '测试结果', '备注']
    
    # 尝试从模板文本中提取表头
    headers = []
    lines = template_text.strip().split('\n')
    
    for line in lines:
        # 查找包含表头关键词的行
        if any(keyword in line for keyword in ['用例编号', '测试项', '测试说明', '前置条件', '测试步骤', '预期结果']):
            # 提取可能的表头
            potential_headers = re.findall(r'[^\s\d\W]+', line)
            for header in potential_headers:
                if len(header) >= 2 and header not in headers:
                    headers.append(header)
    
    # 如果无法识别表头或识别不完整，使用默认表头
    if not headers or len(headers) < 4:
        print("  未能完全识别表头，使用默认表头")
        headers = default_headers
    else:
        print(f"  成功识别表头: {headers}")
    
    return headers

def extract_requirements_from_text(text):
    """从文本中提取需求信息"""
    requirements = []
    
    # 查找需求描述段落
    paragraphs = re.split(r'\n\s*\n', text)
    
    for para in paragraphs:
        para = para.strip()
        if len(para) < 15:  # 忽略太短的段落
            continue
        
        # 添加所有有意义的段落作为潜在需求
        if len(para) > 20 and not para.startswith('http') and not re.match(r'^[0-9.]+$', para):
            requirements.append(para)
    
    return requirements

def analyze_requirements(image_files):
    """分析需求文档图片，提取功能点"""
    print("\n开始分析需求文档...")
    modules = {}
    
    # 首先处理测试用例模板
    template_text = extract_text_from_image(template_path)
    headers = parse_template(template_text)
    
    # 定义模块名称映射
    module_mapping = {
        '应用管理子系统1': '应用管理子系统-应用管理',
        '应用管理子系统2': '应用管理子系统-应用配置',
        '应用管理子系统3': '应用管理子系统-应用监控'
    }
    
    # 按模块分组处理图片
    for img_file in image_files:
        if '测试用例模板' in img_file:
            continue
        
        # 提取模块名称
        module_name = os.path.basename(img_file).replace('.png', '')
        
        # 提取模块主分类
        main_module = re.match(r'应用管理子系统(\d+)', module_name)
        if main_module:
            main_module = f"应用管理子系统{main_module.group(1)}"
            # 使用映射替换模块名称
            if main_module in module_mapping:
                main_module = module_mapping[main_module]
        else:
            main_module = module_name
        
        # 读取图片内容
        img_text = extract_text_from_image(os.path.join(image_dir, img_file))
        
        # 如果是新模块，初始化
        if main_module not in modules:
            modules[main_module] = []
        
        # 提取需求
        requirements = extract_requirements_from_text(img_text)
        modules[main_module].extend(requirements)
    
    # 如果没有足够的需求，添加一些通用需求
    for module in modules:
        if len(modules[module]) < 5:
            print(f"  为模块 {module} 添加通用需求")
            if '应用管理' in module:
                modules[module].extend([
                    "应用管理功能：系统应支持应用的创建、查询、修改和删除操作，实现应用全生命周期管理。",
                    "应用列表展示：系统应提供应用列表页面，展示所有应用的基本信息，并支持分页、排序和筛选功能。",
                    "应用详情查看：系统应提供应用详情页面，展示应用的详细信息，包括基本信息、配置信息、部署信息等。",
                    "应用版本管理：系统应支持应用版本的管理，包括版本创建、查询、回滚等功能。",
                    "应用权限管理：系统应支持对应用的权限管理，控制不同角色用户对应用的操作权限。"
                ])
            elif '配置' in module:
                modules[module].extend([
                    "应用配置管理：系统应支持对应用配置的管理，包括配置的创建、查询、修改和删除。",
                    "配置模板管理：系统应提供配置模板功能，支持用户创建和使用配置模板，提高配置效率。",
                    "配置版本管理：系统应支持配置版本的管理，包括版本历史查看、比较和回滚功能。",
                    "配置导入导出：系统应支持配置的导入导出功能，方便用户在不同环境间迁移配置。",
                    "配置验证功能：系统应提供配置验证功能，确保配置的正确性和有效性。"
                ])
            elif '监控' in module:
                modules[module].extend([
                    "应用监控功能：系统应提供应用运行状态的实时监控功能，展示应用的关键指标。",
                    "监控指标配置：系统应支持监控指标的自定义配置，满足不同应用的监控需求。",
                    "告警规则管理：系统应支持告警规则的配置和管理，当监控指标超过阈值时触发告警。",
                    "监控数据展示：系统应提供多种图表方式展示监控数据，支持数据的导出和分析。",
                    "历史数据查询：系统应支持监控数据的历史查询功能，便于用户分析应用的历史运行情况。"
                ])
    
    return modules, headers

def generate_test_cases(modules, headers):
    """根据需求生成测试用例"""
    print("\n开始生成测试用例...")
    test_cases = {}
    
    # 测试用例类型
    test_types = {
        'functional': '功能测试',
        'boundary': '边界值测试',
        'invalid': '无效输入测试',
        'permission': '权限控制测试',
        'performance': '性能测试',
        'ui': '界面测试',
        'compatibility': '兼容性测试',
        'security': '安全性测试'
    }
    
    for module, requirements in modules.items():
        test_cases[module] = []
        module_case_id = 1
        
        print(f"处理模块: {module}, 需求数量: {len(requirements)}")
        
        # 对每个需求生成测试用例
        for req in requirements:
            # 提取功能点
            req = req.strip()
            if len(req) < 15:  # 忽略太短的需求
                continue
            
            # 1. 基本功能测试
            test_case = {
                '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                '测试项': f'{test_types["functional"]}',
                '测试说明': req[:200] + '...' if len(req) > 200 else req,
                '前置条件': '系统正常运行，用户已登录系统并具有相应权限',
                '测试步骤': '1. 进入应用管理子系统\n2. 选择相应功能模块\n3. 执行相关操作\n4. 验证操作结果',
                '预期结果': '功能正常响应，操作成功完成，数据正确保存和显示',
                '测试结果': '',
                '备注': ''
            }
            test_cases[module].append(test_case)
            module_case_id += 1
            
            # 2. 边界值测试（如果需求涉及数据输入）
            if re.search(r'输入|填写|数量|大小|限制|长度', req):
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["boundary"]}',
                    '测试说明': f'针对"{req[:100]}..."的边界值测试',
                    '前置条件': '系统正常运行，用户已登录系统并具有相应权限',
                    '测试步骤': '1. 进入应用管理子系统\n2. 选择相应功能模块\n3. 输入边界值数据（最大值、最小值、临界值）\n4. 提交表单',
                    '预期结果': '系统能够正确处理边界值数据，给出适当的响应或提示',
                    '测试结果': '',
                    '备注': '测试数据：最大允许值、最大允许值+1、最小允许值、最小允许值-1、0值等'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
            
            # 3. 无效输入测试
            if re.search(r'输入|填写|提交|表单', req):
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["invalid"]}',
                    '测试说明': f'针对"{req[:100]}..."的无效输入测试',
                    '前置条件': '系统正常运行，用户已登录系统并具有相应权限',
                    '测试步骤': '1. 进入应用管理子系统\n2. 选择相应功能模块\n3. 输入无效数据（空值、格式错误、特殊字符等）\n4. 提交表单',
                    '预期结果': '系统给出适当的错误提示，不接受无效输入，页面状态保持稳定',
                    '测试结果': '',
                    '备注': '无效输入包括：空值、特殊字符、格式错误的数据、超长字符等'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
            
            # 4. 权限控制测试
            if re.search(r'权限|角色|管理员|用户|访问控制', req):
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["permission"]}',
                    '测试说明': f'针对"{req[:100]}..."的权限控制测试',
                    '前置条件': '系统正常运行，准备不同权限的用户账号',
                    '测试步骤': '1. 分别使用不同权限的用户账号登录系统\n2. 尝试访问功能模块\n3. 尝试执行各种操作',
                    '预期结果': '系统根据用户权限正确控制功能访问，无权限用户无法执行相应操作或查看相应数据',
                    '测试结果': '',
                    '备注': '测试不同角色：管理员、普通用户、访客等'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
            
            # 5. 性能测试
            if re.search(r'性能|响应时间|并发|负载', req) or '管理' in module:
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["performance"]}',
                    '测试说明': f'针对"{req[:100]}..."的性能测试',
                    '前置条件': '系统正常运行，准备测试数据和性能监控工具',
                    '测试步骤': '1. 模拟多用户并发访问系统\n2. 执行大批量数据操作\n3. 监控系统响应时间和资源占用',
                    '预期结果': '系统在高负载下保持稳定，响应时间在可接受范围内，资源占用合理',
                    '测试结果': '',
                    '备注': '测试指标：响应时间<2秒，支持50用户并发访问'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
            
            # 6. 界面测试
            if re.search(r'界面|显示|页面|布局|列表|表单', req):
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["ui"]}',
                    '测试说明': f'针对"{req[:100]}..."的界面测试',
                    '前置条件': '系统正常运行，用户已登录系统',
                    '测试步骤': '1. 进入应用管理子系统\n2. 打开相关功能页面\n3. 检查页面布局、元素排列、字体颜色等\n4. 尝试调整浏览器窗口大小',
                    '预期结果': '页面布局合理，元素对齐，字体清晰，颜色协调，响应式设计正常',
                    '测试结果': '',
                    '备注': '在不同分辨率下测试：1920x1080, 1366x768, 移动设备等'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
            
            # 7. 兼容性测试
            if re.search(r'浏览器|兼容|平台', req) or module_case_id % 7 == 0:  # 每7个用例添加一个兼容性测试
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["compatibility"]}',
                    '测试说明': f'针对"{req[:100]}..."的兼容性测试',
                    '前置条件': '准备不同浏览器和设备',
                    '测试步骤': '1. 在不同浏览器中打开系统（Chrome, Firefox, Edge等）\n2. 执行相关功能操作\n3. 验证功能和界面表现',
                    '预期结果': '系统在各种浏览器中功能正常，界面一致',
                    '测试结果': '',
                    '备注': '测试环境：Chrome最新版, Firefox最新版, Edge最新版'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
            
            # 8. 安全性测试
            if re.search(r'安全|登录|认证|授权|敏感数据', req) or module_case_id % 8 == 0:  # 每8个用例添加一个安全性测试
                test_case = {
                    '用例编号': f'{module.split("-")[-1][:2]}-{module_case_id:03d}',
                    '测试项': f'{test_types["security"]}',
                    '测试说明': f'针对"{req[:100]}..."的安全性测试',
                    '前置条件': '系统正常运行',
                    '测试步骤': '1. 尝试未授权访问功能和数据\n2. 尝试SQL注入等常见攻击\n3. 检查敏感数据传输和存储方式',
                    '预期结果': '系统能够抵御未授权访问和常见攻击，敏感数据传输加密，存储安全',
                    '测试结果': '',
                    '备注': '测试项目：未授权访问、SQL注入、XSS攻击、CSRF攻击等'
                }
                test_cases[module].append(test_case)
                module_case_id += 1
    
    return test_cases

def create_test_doc(test_cases, headers, output_path):
    """创建Word格式的测试用例文档"""
    print("\n创建测试用例文档...")
    doc = docx.Document()
    
    # 设置文档标题
    title = doc.add_heading('应用管理子系统测试用例', level=0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 为每个模块创建测试用例表格
    for module, cases in test_cases.items():
        if not cases:
            continue
            
        # 添加模块标题
        doc.add_heading(f'{module}测试用例', level=1)
        
        # 创建表格
        table = doc.add_table(rows=1, cols=len(headers))
        table.style = 'Table Grid'
        
        # 设置表头
        header_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            header_cells[i].text = header
            # 设置表头样式
            for paragraph in header_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.bold = True
        
        # 添加测试用例
        for case in cases:
            row_cells = table.add_row().cells
            for i, header in enumerate(headers):
                if header in case:
                    row_cells[i].text = case[header]
                elif header == '用例编号' and '用例编号' in case:
                    row_cells[i].text = case['用例编号']
                elif header == '测试项目' and '测试项' in case:
                    row_cells[i].text = case['测试项']
                elif header == '用例名称' and '测试项' in case:
                    row_cells[i].text = case['测试项']
                elif header == '测试步骤' and '测试步骤' in case:
                    row_cells[i].text = case['测试步骤']
                elif header == '步骤' and '测试步骤' in case:
                    row_cells[i].text = case['测试步骤']
                elif header == '步骤工述' and '测试步骤' in case:
                    row_cells[i].text = case['测试步骤']
                elif header == '预期结果' and '预期结果' in case:
                    row_cells[i].text = case['预期结果']
        
        # 添加分隔
        doc.add_paragraph('')
    
    # 保存文档
    doc.save(output_path)
    print(f"测试用例文档已保存至: {output_path}")

def main():
    # 获取目录下所有PNG图片
    image_files = [f for f in os.listdir(image_dir) if f.endswith('.png')]
    
    # 分析需求图片
    modules, headers = analyze_requirements(image_files)
    
    # 生成测试用例
    test_cases = generate_test_cases(modules, headers)
    
    # 创建测试用例文档
    create_test_doc(test_cases, headers, output_doc)

if __name__ == "__main__":
    main() 